-- Create video_history table to store user's generated videos
CREATE TABLE IF NOT EXISTS public.video_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    prompt TEXT NOT NULL,
    original_video_url TEXT NOT NULL, -- Original external URL
    original_download_url TEXT NOT NULL, -- Original external download URL
    stored_video_url TEXT, -- URL in Supabase storage (nullable initially)
    stored_video_path TEXT, -- Path in Supabase storage
    file_size BIGINT, -- File size in bytes
    duration REAL, -- Video duration in seconds
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    error_message TEXT, -- Error message if processing failed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
    processed_at TIMESTAMP WITH TIME ZONE -- When video was successfully stored
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_video_history_user_id ON public.video_history(user_id);
CREATE INDEX IF NOT EXISTS idx_video_history_created_at ON public.video_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_video_history_status ON public.video_history(status);

-- Enable Row Level Security
ALTER TABLE public.video_history ENABLE ROW LEVEL SECURITY;

-- RLS Policies for video_history
-- Users can only see their own video history
CREATE POLICY "Users can view own video history" ON public.video_history
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own video history
CREATE POLICY "Users can insert own video history" ON public.video_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own video history
CREATE POLICY "Users can update own video history" ON public.video_history
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own video history
CREATE POLICY "Users can delete own video history" ON public.video_history
    FOR DELETE USING (auth.uid() = user_id);

-- Admins can view all video history
CREATE POLICY "Admins can view all video history" ON public.video_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc', NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE OR REPLACE TRIGGER update_video_history_updated_at
    BEFORE UPDATE ON public.video_history
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create storage bucket for videos (this will be done via API)
-- The bucket will be created programmatically with proper policies

-- Function to clean up old video history (optional, for maintenance)
CREATE OR REPLACE FUNCTION public.cleanup_old_video_history(days_old INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete video history older than specified days
    DELETE FROM public.video_history 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_old;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a view for video statistics (optional)
CREATE OR REPLACE VIEW public.video_stats AS
SELECT 
    user_id,
    COUNT(*) as total_videos,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_videos,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_videos,
    COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_videos,
    SUM(file_size) as total_storage_used,
    AVG(duration) as avg_duration,
    MIN(created_at) as first_video_date,
    MAX(created_at) as last_video_date
FROM public.video_history
GROUP BY user_id;

-- Enable RLS on the view
ALTER VIEW public.video_stats SET (security_invoker = true);

-- Grant necessary permissions
GRANT SELECT ON public.video_history TO authenticated;
GRANT INSERT ON public.video_history TO authenticated;
GRANT UPDATE ON public.video_history TO authenticated;
GRANT DELETE ON public.video_history TO authenticated;
GRANT SELECT ON public.video_stats TO authenticated;
