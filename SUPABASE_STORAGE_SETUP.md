# Supabase 存储桶设置指南

## 概述

本指南将帮助您设置 Supabase 存储桶来存储生成的视频文件。视频存储功能需要正确配置存储桶和权限策略。

## 前提条件

- 已完成基本的 Supabase 项目设置
- 已配置用户认证系统
- 已设置环境变量（包括 `SUPABASE_SERVICE_ROLE_KEY`）

## 步骤 1: 创建存储桶

1. 登录到 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择您的项目
3. 在左侧菜单中点击 "Storage"
4. 点击 "Create a new bucket"
5. 设置以下参数：
   - **Bucket name**: `videos`
   - **Public bucket**: ✅ 勾选（允许公开访问）
   - **File size limit**: `100 MB`（可根据需要调整）
   - **Allowed MIME types**: `video/mp4, video/webm, video/quicktime`

## 步骤 2: 配置存储桶策略

在 Supabase Dashboard 中：

1. 进入 "Storage" → "Policies"
2. 选择 `videos` 存储桶
3. 添加以下策略：

### 策略 1: 允许认证用户上传视频

```sql
-- 策略名称: Allow authenticated users to upload videos
-- 操作: INSERT
-- 目标角色: authenticated

CREATE POLICY "Allow authenticated users to upload videos" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (
  bucket_id = 'videos' AND
  auth.uid()::text = (storage.foldername(name))[1]
);
```

### 策略 2: 允许用户读取自己的视频

```sql
-- 策略名称: Allow users to read their own videos
-- 操作: SELECT
-- 目标角色: authenticated

CREATE POLICY "Allow users to read their own videos" ON storage.objects
FOR SELECT TO authenticated
USING (
  bucket_id = 'videos' AND
  auth.uid()::text = (storage.foldername(name))[1]
);
```

### 策略 3: 允许用户删除自己的视频

```sql
-- 策略名称: Allow users to delete their own videos
-- 操作: DELETE
-- 目标角色: authenticated

CREATE POLICY "Allow users to delete their own videos" ON storage.objects
FOR DELETE TO authenticated
USING (
  bucket_id = 'videos' AND
  auth.uid()::text = (storage.foldername(name))[1]
);
```

### 策略 4: 允许公开读取（可选）

如果您希望视频可以公开访问（不需要认证），可以添加此策略：

```sql
-- 策略名称: Allow public read access to videos
-- 操作: SELECT
-- 目标角色: public

CREATE POLICY "Allow public read access to videos" ON storage.objects
FOR SELECT TO public
USING (bucket_id = 'videos');
```

## 步骤 3: 验证设置

### 方法 1: 使用 Supabase Dashboard

1. 在 Storage 页面尝试手动上传一个测试视频文件
2. 检查文件是否成功上传
3. 尝试获取公开 URL 并在浏览器中访问

### 方法 2: 使用诊断 API

应用中包含了诊断工具，您可以通过以下方式测试：

```bash
# 测试存储桶连接
curl -X POST http://localhost:3000/api/admin/storage-test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 步骤 4: 环境变量检查

确保您的 `.env.local` 文件包含以下变量：

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

**重要**: `SUPABASE_SERVICE_ROLE_KEY` 是必需的，用于服务端的存储操作。

## 常见问题排查

### 问题 1: 视频上传失败

**可能原因**:
- 存储桶不存在或名称错误
- 权限策略配置错误
- 文件大小超过限制

**解决方案**:
1. 检查存储桶名称是否为 `videos`
2. 验证权限策略是否正确配置
3. 检查文件大小限制

### 问题 2: 无法访问存储的视频

**可能原因**:
- 存储桶未设置为公开
- 缺少公开读取策略

**解决方案**:
1. 确保存储桶设置为公开
2. 添加公开读取策略（策略 4）

### 问题 3: 权限被拒绝

**可能原因**:
- 用户未认证
- 权限策略配置错误
- 服务角色密钥错误

**解决方案**:
1. 检查用户认证状态
2. 验证权限策略
3. 确认服务角色密钥正确

## 文件结构

视频文件将按以下结构存储：

```
videos/
├── user_id_1/
│   ├── video_history_id_1.mp4
│   ├── video_history_id_2.mp4
│   └── ...
├── user_id_2/
│   ├── video_history_id_3.mp4
│   └── ...
└── ...
```

## 监控和维护

### 存储使用量监控

1. 在 Supabase Dashboard 的 "Settings" → "Usage" 中监控存储使用量
2. 设置使用量警报
3. 定期清理旧的或不需要的视频文件

### 定期清理

可以创建定期任务来清理旧的视频文件：

```sql
-- 删除 30 天前的视频记录和文件
SELECT public.cleanup_old_video_history(30);
```

## 安全注意事项

1. **服务角色密钥**: 绝不要在客户端代码中暴露服务角色密钥
2. **权限策略**: 确保用户只能访问自己的视频文件
3. **文件大小限制**: 设置合理的文件大小限制以防止滥用
4. **MIME 类型限制**: 只允许视频文件类型

## 下一步

完成存储桶设置后：

1. 重启您的应用程序
2. 测试视频生成和存储功能
3. 检查视频历史记录页面
4. 监控存储使用量

如果遇到问题，请查看应用程序日志或使用诊断工具进行排查。
