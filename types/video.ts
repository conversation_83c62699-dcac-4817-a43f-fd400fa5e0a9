export interface VideoHistory {
  id: string;
  user_id: string;
  prompt: string;
  original_video_url: string;
  original_download_url: string;
  stored_video_url?: string;
  stored_video_path?: string;
  file_size?: number;
  duration?: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error_message?: string;
  created_at: string;
  updated_at: string;
  processed_at?: string;
}

export interface VideoHistoryResponse {
  success: boolean;
  videos: VideoHistory[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface Video {
  id: string;
  prompt: string;
  videoUrl: string;
  downloadUrl: string;
  createdAt: Date;
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  fileSize?: number;
  storedVideoUrl?: string;
}

export interface VideoGeneratorResult {
  prompt: string;
  videoUrl: string;
  downloadUrl: string;
  timestamp: Date;
  videoHistoryId?: string;
}
