import { useState, useEffect, useCallback } from 'react';
import { VideoHistory, VideoHistoryResponse } from '@/types/video';

interface UseVideoHistoryOptions {
  page?: number;
  limit?: number;
  status?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useVideoHistory(options: UseVideoHistoryOptions = {}) {
  const {
    page = 1,
    limit = 10,
    status,
    autoRefresh = false,
    refreshInterval = 30000 // 30 seconds
  } = options;

  const [videos, setVideos] = useState<VideoHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  const fetchVideos = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (status) {
        params.append('status', status);
      }

      const response = await fetch(`/api/video-history?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch video history');
      }

      const data: VideoHistoryResponse = await response.json();

      if (data.success) {
        setVideos(data.videos);
        setPagination(data.pagination);
      } else {
        throw new Error('Failed to fetch video history');
      }
    } catch (err) {
      console.error('Error fetching video history:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [page, limit, status]);

  const deleteVideo = useCallback(async (videoId: string) => {
    try {
      const response = await fetch('/api/video-history', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoId }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete video');
      }

      const data = await response.json();

      if (data.success) {
        // Remove video from local state
        setVideos(prev => prev.filter(video => video.id !== videoId));
        // Update pagination total
        setPagination(prev => ({
          ...prev,
          total: prev.total - 1
        }));
        return true;
      } else {
        throw new Error(data.error || 'Failed to delete video');
      }
    } catch (err) {
      console.error('Error deleting video:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    }
  }, []);

  const refreshVideos = useCallback(() => {
    fetchVideos();
  }, [fetchVideos]);

  // Initial fetch
  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchVideos();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchVideos]);

  return {
    videos,
    loading,
    error,
    pagination,
    deleteVideo,
    refreshVideos,
    fetchVideos
  };
}

export function useVideoDetails(videoId: string) {
  const [video, setVideo] = useState<VideoHistory | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchVideo = useCallback(async () => {
    if (!videoId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/video-history/${videoId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch video details');
      }

      const data = await response.json();

      if (data.success) {
        setVideo(data.video);
      } else {
        throw new Error('Failed to fetch video details');
      }
    } catch (err) {
      console.error('Error fetching video details:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [videoId]);

  useEffect(() => {
    fetchVideo();
  }, [fetchVideo]);

  return {
    video,
    loading,
    error,
    refetch: fetchVideo
  };
}
