'use client';

import React, { useState } from 'react';
import { useVideoHistory } from '@/hooks/useVideoHistory';
import { VideoHistory } from '@/types/video';
// Import formatFileSize function locally
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
import UserMenu from '@/app/components/UserMenu';

export default function HistoryPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set());

  const { videos, loading, error, pagination, deleteVideo, refreshVideos } = useVideoHistory({
    page: currentPage,
    limit: 20,
    status: statusFilter || undefined,
    autoRefresh: true,
    refreshInterval: 30000
  });

  const handleSelectVideo = (videoId: string) => {
    const newSelected = new Set(selectedVideos);
    if (newSelected.has(videoId)) {
      newSelected.delete(videoId);
    } else {
      newSelected.add(videoId);
    }
    setSelectedVideos(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedVideos.size === videos.length) {
      setSelectedVideos(new Set());
    } else {
      setSelectedVideos(new Set(videos.map(v => v.id)));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedVideos.size === 0) return;
    
    if (confirm(`确定要删除选中的 ${selectedVideos.size} 个视频吗？此操作无法撤销。`)) {
      const deletePromises = Array.from(selectedVideos).map(videoId => deleteVideo(videoId));
      await Promise.all(deletePromises);
      setSelectedVideos(new Set());
    }
  };

  const getVideoUrl = (video: VideoHistory) => {
    return video.stored_video_url || video.original_video_url;
  };

  const getDownloadUrl = (video: VideoHistory) => {
    return video.stored_video_url || video.original_download_url;
  };

  const getStatusBadge = (status: VideoHistory['status']) => {
    const statusConfig = {
      pending: { text: '等待中', color: 'bg-yellow-500' },
      processing: { text: '处理中', color: 'bg-blue-500' },
      completed: { text: '已完成', color: 'bg-green-500' },
      failed: { text: '失败', color: 'bg-red-500' }
    };

    const config = statusConfig[status];
    return (
      <span className={`inline-block px-2 py-1 text-xs text-white rounded ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const totalStorageUsed = videos
    .filter(v => v.file_size)
    .reduce((total, v) => total + (v.file_size || 0), 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <UserMenu />
      
      <div className="container mx-auto px-4 py-12">
        <header className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            视频历史记录
          </h1>
          <p className="text-gray-300 text-lg">
            管理您的所有生成视频
          </p>
        </header>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold text-white">{pagination.total}</div>
            <div className="text-gray-400 text-sm">总视频数</div>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold text-green-400">
              {videos.filter(v => v.status === 'completed').length}
            </div>
            <div className="text-gray-400 text-sm">已完成</div>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-400">
              {videos.filter(v => v.status === 'processing').length}
            </div>
            <div className="text-gray-400 text-sm">处理中</div>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold text-white">
              {formatFileSize(totalStorageUsed)}
            </div>
            <div className="text-gray-400 text-sm">存储使用</div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => {
                setStatusFilter(e.target.value);
                setCurrentPage(1);
              }}
              className="px-4 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-500"
            >
              <option value="">所有状态</option>
              <option value="pending">等待中</option>
              <option value="processing">处理中</option>
              <option value="completed">已完成</option>
              <option value="failed">失败</option>
            </select>
            
            <button
              onClick={refreshVideos}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? '刷新中...' : '刷新'}
            </button>
          </div>

          {selectedVideos.size > 0 && (
            <div className="flex gap-2">
              <span className="px-4 py-2 bg-gray-700 text-white rounded">
                已选择 {selectedVideos.size} 个
              </span>
              <button
                onClick={handleBulkDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                批量删除
              </button>
            </div>
          )}
        </div>

        {loading && videos.length === 0 ? (
          <div className="text-center text-white text-lg">加载中...</div>
        ) : error ? (
          <div className="text-center">
            <div className="text-red-400 text-lg">加载失败: {error}</div>
            <button 
              onClick={refreshVideos}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              重试
            </button>
          </div>
        ) : videos.length === 0 ? (
          <div className="text-center text-gray-400 text-lg">
            {statusFilter ? `没有找到状态为"${statusFilter}"的视频` : '还没有生成过视频'}
          </div>
        ) : (
          <>
            {/* Video List */}
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg overflow-hidden">
              <div className="p-4 border-b border-gray-700">
                <label className="flex items-center gap-2 text-white">
                  <input
                    type="checkbox"
                    checked={selectedVideos.size === videos.length && videos.length > 0}
                    onChange={handleSelectAll}
                    className="rounded"
                  />
                  全选
                </label>
              </div>
              
              <div className="divide-y divide-gray-700">
                {videos.map((video) => (
                  <div key={video.id} className="p-4 hover:bg-gray-700/30 transition-colors">
                    <div className="flex items-start gap-4">
                      <input
                        type="checkbox"
                        checked={selectedVideos.has(video.id)}
                        onChange={() => handleSelectVideo(video.id)}
                        className="mt-1 rounded"
                      />
                      
                      <div className="w-32 h-18 bg-gray-900 rounded overflow-hidden flex-shrink-0">
                        <video
                          src={getVideoUrl(video)}
                          className="w-full h-full object-cover"
                          muted
                        />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-4">
                          <div>
                            <p className="text-white font-medium line-clamp-2 mb-1">
                              {video.prompt}
                            </p>
                            <div className="flex items-center gap-2 mb-2">
                              {getStatusBadge(video.status)}
                              {video.file_size && (
                                <span className="text-gray-400 text-sm">
                                  {formatFileSize(video.file_size)}
                                </span>
                              )}
                            </div>
                            <p className="text-gray-500 text-sm">
                              {new Date(video.created_at).toLocaleString('zh-CN')}
                            </p>
                            {video.error_message && (
                              <p className="text-red-400 text-sm mt-1">
                                错误: {video.error_message}
                              </p>
                            )}
                          </div>
                          
                          <div className="flex gap-2">
                            <a
                              href={getDownloadUrl(video)}
                              download
                              className="p-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                              title="下载视频"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                              </svg>
                            </a>
                            <button
                              onClick={() => deleteVideo(video.id)}
                              className="p-2 bg-red-600 text-white rounded hover:bg-red-700"
                              title="删除视频"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex justify-center items-center mt-8 gap-4">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={!pagination.hasPrev || loading}
                  className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50"
                >
                  上一页
                </button>
                
                <span className="text-white">
                  第 {pagination.page} 页，共 {pagination.totalPages} 页
                </span>
                
                <button
                  onClick={() => setCurrentPage(prev => Math.min(pagination.totalPages, prev + 1))}
                  disabled={!pagination.hasNext || loading}
                  className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
