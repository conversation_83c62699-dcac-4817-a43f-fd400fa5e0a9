'use client';

import { useState } from 'react';

interface VideoGeneratorResult {
  prompt: string;
  videoUrl: string;
  downloadUrl: string;
  timestamp: Date;
}

interface VideoGeneratorProps {
  onVideoGenerated: (video: VideoGeneratorResult) => void;
}

export default function VideoGenerator({ onVideoGenerated }: VideoGeneratorProps) {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [status, setStatus] = useState('');
  const [error, setError] = useState('');
  const [translatedPrompt, setTranslatedPrompt] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);

  const generateVideo = async () => {
    if (!prompt.trim()) {
      setError('请输入视频描述');
      return;
    }

    setIsGenerating(true);
    setError('');
    setStatus('正在处理...');

    try {
      // Check if prompt needs translation (non-English text)
      let finalPrompt = prompt;
      const englishOnlyRegex = /^[a-zA-Z0-9\s.,!?'"()-]+$/;
      
      if (!englishOnlyRegex.test(prompt)) {
        setStatus('正在翻译提示词...');
        setIsTranslating(true);
        
        const translateResponse = await fetch('/api/translate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ text: prompt }),
        });

        const translateData = await translateResponse.json();
        
        if (translateData.isTranslated) {
          finalPrompt = translateData.translatedText;
          setTranslatedPrompt(finalPrompt);
          setStatus(`已翻译: ${finalPrompt}`);
          
          // Wait briefly to show translation
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
        
        setIsTranslating(false);
      } else {
        setTranslatedPrompt('');
      }

      setStatus('正在生成视频，这可能需要1-2分钟...');

      // Create an AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, 300000); // 5 minutes timeout

      let data;

      try {
        const response = await fetch('/api/generate-video', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ prompt: finalPrompt }),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Check if response is JSON
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/json')) {
          data = await response.json();
        } else {
          // If not JSON, it might be an error page
          const text = await response.text();
          console.error('Non-JSON response:', text);
          throw new Error(`服务器错误 (${response.status}): 请稍后重试`);
        }

        if (!response.ok) {
          if (response.status === 401) {
            window.location.href = '/auth';
            return;
          }
          if (response.status === 504) {
            throw new Error('请求超时，请稍后重试');
          }
          throw new Error(data.error || `生成失败 (${response.status})`);
        }
      } catch (fetchError) {
        clearTimeout(timeoutId);

        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          throw new Error('请求超时，请稍后重试');
        }
        throw fetchError;
      }

      if (data && data.success && data.videoUrl) {
        const newVideo = {
          prompt,
          videoUrl: data.videoUrl,
          downloadUrl: data.downloadUrl,
          timestamp: new Date(),
        };

        onVideoGenerated(newVideo);
        setPrompt('');
        setTranslatedPrompt('');
        setStatus('视频生成成功！');
        
        setTimeout(() => setStatus(''), 3000);
      } else {
        if (data.fullResponse) {
          console.log('Full API Response:', data.fullResponse);
        }
        throw new Error(data.error || '未能获取视频链接');
      }
    } catch (err) {
      console.error('Video generation error:', err);
      
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('生成失败，请重试');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 shadow-2xl">
      <div className="space-y-6">
        <div>
          <label htmlFor="prompt" className="block text-sm font-medium text-gray-300 mb-2">
            视频描述
          </label>
          <textarea
            id="prompt"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            rows={4}
            placeholder="例如：机械蝴蝶在未来花园中飞舞 (支持多语言自动翻译)"
            disabled={isGenerating}
          />
          {translatedPrompt && (
            <div className="mt-2 p-2 bg-blue-600/20 border border-blue-500/30 rounded text-sm text-blue-300">
              翻译后: {translatedPrompt}
            </div>
          )}
        </div>

        <button
          onClick={generateVideo}
          disabled={isGenerating || !prompt.trim()}
          className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
            isGenerating || !prompt.trim()
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
          }`}
        >
          {isGenerating ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {isTranslating ? '翻译中...' : '生成中...'}
            </span>
          ) : (
            '生成视频'
          )}
        </button>

        {status && (
          <div className="text-center text-green-400 text-sm animate-pulse">
            {status}
          </div>
        )}

        {error && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
            <div className="flex items-center gap-2 text-red-400">
              <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm">{error}</span>
            </div>
            <button
              onClick={() => setError('')}
              className="mt-2 text-xs text-red-300 hover:text-red-200 underline"
            >
              关闭
            </button>
          </div>
        )}
      </div>
    </div>
  );
}