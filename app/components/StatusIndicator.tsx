'use client';

import { useState, useEffect } from 'react';

interface StatusIndicatorProps {
  className?: string;
}

export default function StatusIndicator({ className = '' }: StatusIndicatorProps) {
  const [status, setStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  const checkStatus = async () => {
    try {
      const response = await fetch('/api/health', {
        method: 'GET',
        cache: 'no-cache',
      });
      
      if (response.ok) {
        setStatus('online');
      } else {
        setStatus('offline');
      }
    } catch (error) {
      console.error('Status check failed:', error);
      setStatus('offline');
    } finally {
      setLastCheck(new Date());
    }
  };

  useEffect(() => {
    checkStatus();
    
    // Check status every 30 seconds
    const interval = setInterval(checkStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'offline':
        return 'bg-red-500';
      case 'checking':
        return 'bg-yellow-500 animate-pulse';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'online':
        return '服务正常';
      case 'offline':
        return '服务异常';
      case 'checking':
        return '检查中...';
      default:
        return '未知状态';
    }
  };

  return (
    <div className={`flex items-center gap-2 text-sm ${className}`}>
      <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
      <span className="text-gray-400">{getStatusText()}</span>
      {lastCheck && (
        <span className="text-gray-500 text-xs">
          {lastCheck.toLocaleTimeString('zh-CN')}
        </span>
      )}
    </div>
  );
}
