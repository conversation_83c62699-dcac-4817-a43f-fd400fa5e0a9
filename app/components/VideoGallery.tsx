'use client';

import React, { useState } from 'react';
import { useVideoHistory } from '@/hooks/useVideoHistory';
import { VideoHistory } from '@/types/video';
// Import formatFileSize function locally
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

interface VideoGalleryProps {
  refreshTrigger?: number; // Used to trigger refresh from parent
}

export default function VideoGallery({ refreshTrigger }: VideoGalleryProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const { videos, loading, error, pagination, deleteVideo, refreshVideos } = useVideoHistory({
    page: currentPage,
    limit: 12,
    autoRefresh: true,
    refreshInterval: 30000
  });

  // Refresh when trigger changes
  React.useEffect(() => {
    if (refreshTrigger) {
      refreshVideos();
    }
  }, [refreshTrigger, refreshVideos]);

  const handleDelete = async (videoId: string) => {
    if (confirm('确定要删除这个视频吗？此操作无法撤销。')) {
      const success = await deleteVideo(videoId);
      if (success) {
        // Optionally show success message
      }
    }
  };

  const getVideoUrl = (video: VideoHistory) => {
    // Prefer stored video URL, fallback to original
    return video.stored_video_url || video.original_video_url;
  };

  const getDownloadUrl = (video: VideoHistory) => {
    // Prefer stored video URL for download, fallback to original
    return video.stored_video_url || video.original_download_url;
  };

  const getStatusBadge = (status: VideoHistory['status']) => {
    const statusConfig = {
      pending: { text: '等待中', color: 'bg-yellow-500' },
      processing: { text: '处理中', color: 'bg-blue-500' },
      completed: { text: '已完成', color: 'bg-green-500' },
      failed: { text: '失败', color: 'bg-red-500' }
    };

    const config = statusConfig[status];
    return (
      <span className={`inline-block px-2 py-1 text-xs text-white rounded ${config.color}`}>
        {config.text}
      </span>
    );
  };

  if (loading && videos.length === 0) {
    return (
      <div className="mt-12 text-center">
        <div className="text-white text-lg">加载历史记录中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-12 text-center">
        <div className="text-red-400 text-lg">加载失败: {error}</div>
        <button
          onClick={refreshVideos}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    );
  }

  if (videos.length === 0) {
    return (
      <div className="mt-12 text-center">
        <div className="text-gray-400 text-lg">还没有生成过视频</div>
        <div className="text-gray-500 text-sm mt-2">生成你的第一个视频吧！</div>
      </div>
    );
  }
  return (
    <div className="mt-12">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">
          视频历史记录 ({pagination.total})
        </h2>
        <button
          onClick={refreshVideos}
          disabled={loading}
          className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50"
        >
          {loading ? '刷新中...' : '刷新'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {videos.map((video) => (
          <div
            key={video.id}
            className="bg-gray-800/50 backdrop-blur-sm rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-shadow duration-300"
          >
            <div className="aspect-video bg-gray-900 relative group">
              <video
                src={getVideoUrl(video)}
                controls
                className="absolute inset-0 w-full h-full object-contain"
                poster=""
              >
                您的浏览器不支持视频播放
              </video>

              {/* Status badge */}
              <div className="absolute top-2 left-2">
                {getStatusBadge(video.status)}
              </div>

              {/* Action buttons */}
              <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <a
                  href={getDownloadUrl(video)}
                  download
                  className="bg-gray-900/80 text-white p-2 rounded-lg hover:bg-gray-800"
                  title="下载视频"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                </a>
                <button
                  onClick={() => handleDelete(video.id)}
                  className="bg-red-600/80 text-white p-2 rounded-lg hover:bg-red-700"
                  title="删除视频"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="p-4">
              <p className="text-gray-300 text-sm line-clamp-2 mb-2">
                {video.prompt}
              </p>
              <div className="flex justify-between items-center text-xs text-gray-500">
                <span>{new Date(video.created_at).toLocaleString('zh-CN')}</span>
                {video.file_size && (
                  <span>{formatFileSize(video.file_size)}</span>
                )}
              </div>
              {video.error_message && (
                <p className="text-red-400 text-xs mt-1 line-clamp-1" title={video.error_message}>
                  错误: {video.error_message}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center items-center mt-8 gap-4">
          <button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={!pagination.hasPrev || loading}
            className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>

          <span className="text-white">
            第 {pagination.page} 页，共 {pagination.totalPages} 页
          </span>

          <button
            onClick={() => setCurrentPage(prev => Math.min(pagination.totalPages, prev + 1))}
            disabled={!pagination.hasNext || loading}
            className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      )}
    </div>
  );
}