'use client';

import { useState, useEffect, useCallback } from 'react';
import { VideoHistory } from '@/types/video';

interface VideoStatusMonitorProps {
  videoHistoryId: string;
  onVideoCompleted?: (video: VideoHistory) => void;
  onClose?: () => void;
}

export default function VideoStatusMonitor({ 
  videoHistoryId, 
  onVideoCompleted, 
  onClose 
}: VideoStatusMonitorProps) {
  const [video, setVideo] = useState<VideoHistory | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retrying, setRetrying] = useState(false);

  const checkStatus = useCallback(async () => {
    try {
      const response = await fetch(`/api/video-status/${videoHistoryId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch video status');
      }

      const data = await response.json();

      if (data.success) {
        setVideo(data.video);
        setError(null);

        // If video is completed, notify parent
        if (data.video.status === 'completed' && onVideoCompleted) {
          onVideoCompleted(data.video);
        }
      } else {
        throw new Error(data.error || 'Failed to fetch video status');
      }
    } catch (err) {
      console.error('Error checking video status:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [videoHistoryId, onVideoCompleted]);

  const handleRetry = async () => {
    setRetrying(true);
    try {
      const response = await fetch('/api/manual-trigger', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoHistoryId }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Manual trigger response:', data);
        // Refresh status after triggering
        setTimeout(() => {
          checkStatus();
        }, 2000);
      } else {
        const errorData = await response.json();
        console.error('Manual trigger failed:', errorData);
      }
    } catch (error) {
      console.error('Error triggering manual retry:', error);
    } finally {
      setRetrying(false);
    }
  };

  useEffect(() => {
    checkStatus();

    // Poll status every 5 seconds if video is still processing
    const interval = setInterval(() => {
      if (video?.status === 'pending' || video?.status === 'processing' || loading) {
        checkStatus();
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [videoHistoryId, video?.status, loading, checkStatus]);

  const getStatusIcon = () => {
    if (loading) return '⏳';
    
    switch (video?.status) {
      case 'pending':
        return '⏳';
      case 'processing':
        return '🔄';
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      default:
        return '❓';
    }
  };

  const getStatusText = () => {
    if (loading) return '检查状态中...';
    
    switch (video?.status) {
      case 'pending':
        return '等待处理';
      case 'processing':
        return '正在生成视频...';
      case 'completed':
        return '生成完成';
      case 'failed':
        return '生成失败';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = () => {
    switch (video?.status) {
      case 'pending':
        return 'text-yellow-400';
      case 'processing':
        return 'text-blue-400';
      case 'completed':
        return 'text-green-400';
      case 'failed':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800/95 backdrop-blur-sm rounded-lg p-4 shadow-xl border border-gray-700 max-w-sm z-50">
      <div className="flex items-start justify-between mb-3">
        <h3 className="text-white font-medium">视频生成状态</h3>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <span className="text-2xl">{getStatusIcon()}</span>
          <span className={`font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>

        {video && (
          <div className="space-y-2 text-sm">
            <div className="text-gray-300 line-clamp-2">
              <span className="text-gray-400">提示词:</span> {video.prompt}
            </div>
            
            <div className="text-gray-400">
              <span>创建时间:</span> {formatTime(video.created_at)}
            </div>
            
            {video.updated_at !== video.created_at && (
              <div className="text-gray-400">
                <span>更新时间:</span> {formatTime(video.updated_at)}
              </div>
            )}

            {video.status === 'failed' && video.error_message && (
              <div className="text-red-400 text-xs">
                <span>错误:</span> {video.error_message}
              </div>
            )}

            {video.status === 'completed' && video.original_video_url && (
              <div className="flex gap-2 mt-3">
                <a
                  href={video.original_video_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                >
                  观看
                </a>
                <a
                  href={video.original_download_url}
                  download
                  className="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 transition-colors"
                >
                  下载
                </a>
              </div>
            )}
          </div>
        )}

        {error && (
          <div className="text-red-400 text-xs">
            错误: {error}
          </div>
        )}

        {video?.status === 'pending' && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs text-gray-400">
              <div className="animate-spin w-3 h-3 border border-gray-400 border-t-transparent rounded-full"></div>
              <span>自动刷新中...</span>
            </div>
            <button
              onClick={handleRetry}
              disabled={retrying}
              className="w-full px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {retrying ? '重试中...' : '手动重试'}
            </button>
          </div>
        )}

        {video?.status === 'processing' && (
          <div className="flex items-center gap-2 text-xs text-gray-400">
            <div className="animate-spin w-3 h-3 border border-gray-400 border-t-transparent rounded-full"></div>
            <span>自动刷新中...</span>
          </div>
        )}
      </div>
    </div>
  );
}
