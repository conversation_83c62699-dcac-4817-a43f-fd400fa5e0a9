import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { videoHistoryId } = await request.json();

    if (!videoHistoryId) {
      return NextResponse.json(
        { error: 'Video history ID is required' },
        { status: 400 }
      );
    }

    console.log(`[RETRY-STORAGE] User ${user.id} requesting retry for video ${videoHistoryId}`);

    // Get video history record to verify ownership
    const { data: videoHistory, error: fetchError } = await supabase
      .from('video_history')
      .select('*')
      .eq('id', videoHistoryId)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !videoHistory) {
      console.error(`[RETRY-STORAGE] Video history not found for ID ${videoHistoryId}:`, fetchError);
      return NextResponse.json(
        { error: 'Video history record not found' },
        { status: 404 }
      );
    }

    // Check if video is in a state that allows retry
    if (videoHistory.status === 'completed') {
      return NextResponse.json(
        { error: 'Video is already stored successfully' },
        { status: 400 }
      );
    }

    if (videoHistory.status === 'processing') {
      return NextResponse.json(
        { error: 'Video is currently being processed' },
        { status: 400 }
      );
    }

    if (!videoHistory.original_download_url) {
      return NextResponse.json(
        { error: 'No download URL available for this video' },
        { status: 400 }
      );
    }

    // Reset status to pending
    await supabase
      .from('video_history')
      .update({
        status: 'pending',
        error_message: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', videoHistoryId);

    // Trigger storage process
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    
    // Use fetch with proper error handling
    const storageResponse = await fetch(`${baseUrl}/api/store-video`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forward the authorization header
        'Authorization': request.headers.get('Authorization') || '',
        'Cookie': request.headers.get('Cookie') || ''
      },
      body: JSON.stringify({ videoHistoryId })
    });

    if (!storageResponse.ok) {
      const errorData = await storageResponse.json().catch(() => ({ error: 'Unknown error' }));
      console.error(`[RETRY-STORAGE] Storage API failed:`, errorData);
      
      // Update status back to failed
      await supabase
        .from('video_history')
        .update({
          status: 'failed',
          error_message: errorData.error || 'Storage retry failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', videoHistoryId);

      return NextResponse.json(
        { error: errorData.error || 'Failed to retry storage' },
        { status: 500 }
      );
    }

    const storageResult = await storageResponse.json();
    console.log(`[RETRY-STORAGE] Storage retry successful for video ${videoHistoryId}`);

    return NextResponse.json({
      success: true,
      message: 'Storage retry initiated successfully',
      result: storageResult
    });

  } catch (error) {
    console.error('[RETRY-STORAGE] Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error during storage retry' },
      { status: 500 }
    );
  }
}
