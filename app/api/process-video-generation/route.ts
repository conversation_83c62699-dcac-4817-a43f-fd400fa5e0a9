import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import { createClient } from '@/utils/supabase/server';

export const maxDuration = 300; // 5 minutes for background processing

export async function POST(request: Request) {
  let videoHistoryId: string | null = null;

  try {
    const body = await request.json();
    videoHistoryId = body.videoHistoryId;
    const prompt = body.prompt;

    if (!videoHistoryId || !prompt) {
      console.error('Missing required parameters:', { videoHistoryId, prompt });
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    console.log('Processing video generation for history ID:', videoHistoryId);

    // Create Supabase client - this API doesn't need user auth since it's internal
    const supabase = await createClient();

    // Update status to processing
    await supabase
      .from('video_history')
      .update({ 
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', videoHistoryId);

    const apiKey = process.env.AIHUBMIX_API_KEY;
    if (!apiKey) {
      throw new Error('API key not configured');
    }

    // Generate video using OpenAI
    const openai = new OpenAI({
      apiKey: apiKey,
      baseURL: 'https://aihubmix.com/v1',
      timeout: 240000, // 4 minutes timeout
    });

    const completion = await openai.chat.completions.create({
      model: 'veo-3',
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      stream: false,
    });

    const responseContent = completion.choices[0].message.content;
    console.log('API Response received for history ID:', videoHistoryId);
    
    // Parse the response to extract video URLs
    const videoUrlMatch = responseContent?.match(/\[▶️ Watch Online\]\((.*?)\)/);
    const downloadUrlMatch = responseContent?.match(/\[⏬ Download Video\]\((.*?)\)/);
    
    if (!videoUrlMatch || !downloadUrlMatch) {
      console.error('Could not parse video URLs from response');
      
      // Update status to failed
      await supabase
        .from('video_history')
        .update({
          status: 'failed',
          error_message: 'Could not parse video URLs from response',
          updated_at: new Date().toISOString()
        })
        .eq('id', videoHistoryId);

      return NextResponse.json({
        success: false,
        error: 'Could not parse video URLs from response',
      });
    }

    // Update video history with URLs
    const { error: updateError } = await supabase
      .from('video_history')
      .update({
        original_video_url: videoUrlMatch[1],
        original_download_url: downloadUrlMatch[1],
        status: 'completed',
        updated_at: new Date().toISOString()
      })
      .eq('id', videoHistoryId);

    if (updateError) {
      console.error('Failed to update video history:', updateError);
      return NextResponse.json(
        { error: 'Failed to update video history' },
        { status: 500 }
      );
    }

    // Trigger background video storage (fire and forget)
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    fetch(`${baseUrl}/api/store-video`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ videoHistoryId: videoHistoryId })
    }).catch(error => {
      console.error('Background video storage failed:', error);
    });

    console.log('Video generation completed for history ID:', videoHistoryId);

    return NextResponse.json({
      success: true,
      videoHistoryId: videoHistoryId,
      videoUrl: videoUrlMatch[1],
      downloadUrl: downloadUrlMatch[1]
    });

  } catch (error) {
    console.error('Error in video generation processing:', error);

    // Try to update the video history status to failed
    if (videoHistoryId) {
      try {
        const supabase = await createClient();
        await supabase
          .from('video_history')
          .update({
            status: 'failed',
            error_message: error instanceof Error ? error.message : 'Unknown error',
            updated_at: new Date().toISOString()
          })
          .eq('id', videoHistoryId);
        console.log('Updated video history status to failed for:', videoHistoryId);
      } catch (updateError) {
        console.error('Failed to update error status:', updateError);
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Video generation failed'
      },
      { status: 500 }
    );
  }
}
