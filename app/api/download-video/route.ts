import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const videoUrl = searchParams.get('url');
    const filename = searchParams.get('filename') || 'video.mp4';

    if (!videoUrl) {
      return NextResponse.json(
        { error: 'Video URL is required' },
        { status: 400 }
      );
    }

    // Validate that the URL is from allowed domains
    const allowedDomains = ['filesystem.site'];
    const urlObj = new URL(videoUrl);
    if (!allowedDomains.includes(urlObj.hostname)) {
      return NextResponse.json(
        { error: 'Invalid video URL domain' },
        { status: 400 }
      );
    }

    console.log('Proxying download for:', videoUrl);

    // Fetch the video from the external URL
    const response = await fetch(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Veo3VideoDownloader/1.0)',
      },
    });

    if (!response.ok) {
      console.error('Failed to fetch video:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to fetch video from external source' },
        { status: response.status }
      );
    }

    // Get the video data
    const videoBuffer = await response.arrayBuffer();
    
    // Return the video with appropriate headers for download
    return new NextResponse(videoBuffer, {
      status: 200,
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'video/mp4',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': videoBuffer.byteLength.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Error in video download proxy:', error);
    
    return NextResponse.json(
      { error: 'Internal server error during video download' },
      { status: 500 }
    );
  }
}
