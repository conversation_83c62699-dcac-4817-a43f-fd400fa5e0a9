import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import { createClient } from '@/utils/supabase/server';

export const maxDuration = 300; // 5 minutes - use full Vercel limit

export async function POST(request: Request) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { prompt } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    console.log('Starting video generation for prompt:', prompt);

    // Create video history record immediately
    const { data: videoHistory, error: historyError } = await supabase
      .from('video_history')
      .insert({
        user_id: user.id,
        prompt: prompt,
        original_video_url: '',
        original_download_url: '',
        status: 'processing'
      })
      .select()
      .single();

    if (historyError) {
      console.error('Failed to create video history:', historyError);
      return NextResponse.json(
        { error: 'Failed to create video record' },
        { status: 500 }
      );
    }

    console.log('Created video history record:', videoHistory.id);

    const apiKey = process.env.AIHUBMIX_API_KEY;
    if (!apiKey) {
      await supabase
        .from('video_history')
        .update({
          status: 'failed',
          error_message: 'API key not configured',
          updated_at: new Date().toISOString()
        })
        .eq('id', videoHistory.id);
      
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      );
    }

    try {
      console.log('Starting OpenAI API call...');
      
      // Generate video with full timeout
      const openai = new OpenAI({
        apiKey: apiKey,
        baseURL: 'https://aihubmix.com/v1',
        timeout: 280000, // 280 seconds (留20秒给其他操作)
      });

      const completion = await openai.chat.completions.create({
        model: 'veo-3',
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: false,
      });

      console.log('OpenAI API call completed');
      const responseContent = completion.choices[0].message.content;
      
      // Parse the response to extract video URLs
      const videoUrlMatch = responseContent?.match(/\[▶️ Watch Online\]\((.*?)\)/);
      const downloadUrlMatch = responseContent?.match(/\[⏬ Download Video\]\((.*?)\)/);
      
      if (!videoUrlMatch || !downloadUrlMatch) {
        console.error('Could not parse video URLs from response');
        
        await supabase
          .from('video_history')
          .update({
            status: 'failed',
            error_message: 'Could not parse video URLs from response',
            updated_at: new Date().toISOString()
          })
          .eq('id', videoHistory.id);

        return NextResponse.json({
          success: false,
          error: 'Could not parse video URLs from response',
        });
      }

      console.log('Parsed video URLs successfully');

      // Update video history with URLs
      await supabase
        .from('video_history')
        .update({
          original_video_url: videoUrlMatch[1],
          original_download_url: downloadUrlMatch[1],
          status: 'completed',
          updated_at: new Date().toISOString(),
          processed_at: new Date().toISOString()
        })
        .eq('id', videoHistory.id);

      console.log('Video generation completed successfully for:', videoHistory.id);

      // Trigger background video storage (fire and forget)
      const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
      fetch(`${baseUrl}/api/store-video`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoHistoryId: videoHistory.id })
      }).catch(error => {
        console.error('Background video storage failed:', error);
      });

      return NextResponse.json({
        success: true,
        videoHistoryId: videoHistory.id,
        videoUrl: videoUrlMatch[1],
        downloadUrl: downloadUrlMatch[1],
        message: '视频生成完成'
      });

    } catch (apiError) {
      console.error('API Error:', apiError);
      
      // Update status to failed
      await supabase
        .from('video_history')
        .update({
          status: 'failed',
          error_message: apiError instanceof Error ? apiError.message : 'API call failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', videoHistory.id);

      if (apiError instanceof Error && (
        apiError.message.includes('timeout') || 
        apiError.message.includes('ETIMEDOUT') ||
        apiError.message.includes('AbortError')
      )) {
        return NextResponse.json({
          success: false,
          error: '视频生成超时，请稍后重试',
          videoHistoryId: videoHistory.id
        }, { status: 408 });
      }

      return NextResponse.json({
        success: false,
        error: apiError instanceof Error ? apiError.message : 'Video generation failed',
        videoHistoryId: videoHistory.id
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in video generation:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate video'
      },
      { status: 500 }
    );
  }
}
