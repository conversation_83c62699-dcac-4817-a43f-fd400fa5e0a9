import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import { createClient } from '@/utils/supabase/server';

export const maxDuration = 300; // 5 minutes function timeout

export async function POST(request: Request) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { prompt } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    const apiKey = process.env.AIHUBMIX_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      );
    }

    console.log('Starting video generation for prompt:', prompt);

    const openai = new OpenAI({
      apiKey: apiKey,
      baseURL: 'https://aihubmix.com/v1',
      timeout: 240000, // 4 minutes timeout
    });

    const completion = await openai.chat.completions.create({
      model: 'veo-3',
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      stream: false,
    });

    const responseContent = completion.choices[0].message.content;
    console.log('API Response:', responseContent);
    
    // Parse the response to extract video URLs
    const videoUrlMatch = responseContent?.match(/\[▶️ Watch Online\]\((.*?)\)/);
    const downloadUrlMatch = responseContent?.match(/\[⏬ Download Video\]\((.*?)\)/);
    
    if (!videoUrlMatch || !downloadUrlMatch) {
      console.error('Could not parse video URLs from response');
      return NextResponse.json({
        success: false,
        error: 'Could not parse video URLs from response',
        fullResponse: responseContent,
      });
    }
    
    // Save to video history
    const { data: videoHistory, error: historyError } = await supabase
      .from('video_history')
      .insert({
        user_id: user.id,
        prompt: prompt,
        original_video_url: videoUrlMatch[1],
        original_download_url: downloadUrlMatch[1],
        status: 'pending'
      })
      .select()
      .single();

    if (historyError) {
      console.error('Failed to save video history:', historyError);
      // Continue anyway, don't fail the request
    }

    // Trigger background video storage (fire and forget)
    if (videoHistory?.id) {
      // Call the store-video API in the background
      // Note: We'll handle authentication in the store-video endpoint
      fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/store-video`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoHistoryId: videoHistory.id })
      }).catch(error => {
        console.error('Background video storage failed:', error);
      });
    }

    return NextResponse.json({
      success: true,
      videoUrl: videoUrlMatch[1],
      downloadUrl: downloadUrlMatch[1],
      videoHistoryId: videoHistory?.id,
      fullResponse: responseContent,
    });
  } catch (error) {
    console.error('Error generating video:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to generate video';
    const errorDetails = error instanceof Error ? error.toString() : String(error);

    // Handle specific error types
    if (error instanceof Error) {
      // Timeout errors
      if (error.message.includes('timeout') || error.message.includes('ETIMEDOUT')) {
        return NextResponse.json(
          {
            success: false,
            error: '请求超时，请稍后重试',
            details: 'Request timeout'
          },
          { status: 504 }
        );
      }

      // Quota errors
      if ('code' in error && error.code === 'insufficient_quota') {
        return NextResponse.json(
          {
            success: false,
            error: '账户余额不足，请充值后再试'
          },
          { status: 402 }
        );
      }

      // Auth errors
      if ('status' in error && error.status === 401) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid API key'
          },
          { status: 401 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: errorDetails
      },
      { status: 500 }
    );
  }
}