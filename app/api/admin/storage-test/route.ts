import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { createAdminClient } from '@/utils/supabase/admin-client';
import { checkIsAdmin } from '@/utils/supabase/admin';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const isAdmin = await checkIsAdmin(user.id);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    const diagnostics = {
      timestamp: new Date().toISOString(),
      tests: [] as any[],
      summary: {
        passed: 0,
        failed: 0,
        warnings: 0
      }
    };

    // Test 1: Check environment variables
    const envTest = {
      name: 'Environment Variables',
      status: 'passed' as 'passed' | 'failed' | 'warning',
      details: {} as any,
      message: ''
    };

    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
      const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      envTest.details = {
        supabaseUrl: supabaseUrl ? '✓ Set' : '✗ Missing',
        anonKey: anonKey ? '✓ Set' : '✗ Missing',
        serviceKey: serviceKey ? '✓ Set' : '✗ Missing'
      };

      if (!supabaseUrl || !anonKey || !serviceKey) {
        envTest.status = 'failed';
        envTest.message = 'Missing required environment variables';
      } else {
        envTest.message = 'All environment variables are set';
      }
    } catch (error) {
      envTest.status = 'failed';
      envTest.message = 'Error checking environment variables';
    }

    diagnostics.tests.push(envTest);

    // Test 2: Check Supabase connection
    const connectionTest = {
      name: 'Supabase Connection',
      status: 'passed' as 'passed' | 'failed' | 'warning',
      details: {} as any,
      message: ''
    };

    try {
      const adminSupabase = createAdminClient();
      const { data, error } = await adminSupabase.storage.listBuckets();
      
      if (error) {
        connectionTest.status = 'failed';
        connectionTest.message = `Connection failed: ${error.message}`;
        connectionTest.details.error = error;
      } else {
        connectionTest.status = 'passed';
        connectionTest.message = 'Successfully connected to Supabase';
        connectionTest.details.bucketsFound = data?.length || 0;
      }
    } catch (error) {
      connectionTest.status = 'failed';
      connectionTest.message = 'Failed to connect to Supabase';
      connectionTest.details.error = error instanceof Error ? error.message : 'Unknown error';
    }

    diagnostics.tests.push(connectionTest);

    // Test 3: Check videos bucket exists
    const bucketTest = {
      name: 'Videos Bucket',
      status: 'passed' as 'passed' | 'failed' | 'warning',
      details: {} as any,
      message: ''
    };

    try {
      const adminSupabase = createAdminClient();
      const { data: buckets, error } = await adminSupabase.storage.listBuckets();
      
      if (error) {
        bucketTest.status = 'failed';
        bucketTest.message = `Failed to list buckets: ${error.message}`;
      } else {
        const videosBucket = buckets?.find(bucket => bucket.name === 'videos');
        
        if (videosBucket) {
          bucketTest.status = 'passed';
          bucketTest.message = 'Videos bucket exists';
          bucketTest.details = {
            bucketId: videosBucket.id,
            isPublic: videosBucket.public,
            createdAt: videosBucket.created_at
          };
        } else {
          bucketTest.status = 'failed';
          bucketTest.message = 'Videos bucket does not exist';
          bucketTest.details.availableBuckets = buckets?.map(b => b.name) || [];
        }
      }
    } catch (error) {
      bucketTest.status = 'failed';
      bucketTest.message = 'Error checking videos bucket';
      bucketTest.details.error = error instanceof Error ? error.message : 'Unknown error';
    }

    diagnostics.tests.push(bucketTest);

    // Test 4: Test file upload
    const uploadTest = {
      name: 'File Upload Test',
      status: 'passed' as 'passed' | 'failed' | 'warning',
      details: {} as any,
      message: ''
    };

    try {
      const adminSupabase = createAdminClient();
      const testFileName = `test-${Date.now()}.txt`;
      const testFilePath = `${user.id}/${testFileName}`;
      const testContent = 'This is a test file for storage diagnostics';
      
      const { data: uploadData, error: uploadError } = await adminSupabase.storage
        .from('videos')
        .upload(testFilePath, testContent, {
          contentType: 'text/plain',
          upsert: false
        });

      if (uploadError) {
        uploadTest.status = 'failed';
        uploadTest.message = `Upload failed: ${uploadError.message}`;
        uploadTest.details.error = uploadError;
      } else {
        uploadTest.status = 'passed';
        uploadTest.message = 'Test file uploaded successfully';
        uploadTest.details.uploadPath = uploadData.path;

        // Clean up test file
        await adminSupabase.storage
          .from('videos')
          .remove([testFilePath]);
        
        uploadTest.details.cleanedUp = true;
      }
    } catch (error) {
      uploadTest.status = 'failed';
      uploadTest.message = 'Error during upload test';
      uploadTest.details.error = error instanceof Error ? error.message : 'Unknown error';
    }

    diagnostics.tests.push(uploadTest);

    // Test 5: Check video history table
    const dbTest = {
      name: 'Database Connection',
      status: 'passed' as 'passed' | 'failed' | 'warning',
      details: {} as any,
      message: ''
    };

    try {
      const { data, error } = await supabase
        .from('video_history')
        .select('count(*)', { count: 'exact', head: true });

      if (error) {
        dbTest.status = 'failed';
        dbTest.message = `Database query failed: ${error.message}`;
        dbTest.details.error = error;
      } else {
        dbTest.status = 'passed';
        dbTest.message = 'Database connection successful';
        dbTest.details.videoHistoryCount = data || 0;
      }
    } catch (error) {
      dbTest.status = 'failed';
      dbTest.message = 'Error checking database';
      dbTest.details.error = error instanceof Error ? error.message : 'Unknown error';
    }

    diagnostics.tests.push(dbTest);

    // Calculate summary
    diagnostics.tests.forEach(test => {
      if (test.status === 'passed') {
        diagnostics.summary.passed++;
      } else if (test.status === 'failed') {
        diagnostics.summary.failed++;
      } else {
        diagnostics.summary.warnings++;
      }
    });

    return NextResponse.json({
      success: true,
      diagnostics
    });

  } catch (error) {
    console.error('Storage diagnostics error:', error);
    return NextResponse.json(
      { error: 'Failed to run storage diagnostics' },
      { status: 500 }
    );
  }
}
