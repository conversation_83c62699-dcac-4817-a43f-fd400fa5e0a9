import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { videoHistoryId } = await request.json();

    if (!videoHistoryId) {
      return NextResponse.json(
        { error: 'Video history ID is required' },
        { status: 400 }
      );
    }

    console.log('Manual trigger for video history ID:', videoHistoryId);

    // Get video history record
    const { data: videoHistory, error: fetchError } = await supabase
      .from('video_history')
      .select('*')
      .eq('id', videoHistoryId)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !videoHistory) {
      console.error('Video history not found:', fetchError);
      return NextResponse.json(
        { error: 'Video history record not found' },
        { status: 404 }
      );
    }

    // Trigger background video generation
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    console.log('Triggering background generation for:', videoHistory.id);
    console.log('Base URL:', baseUrl);
    console.log('Prompt:', videoHistory.prompt);
    
    const response = await fetch(`${baseUrl}/api/process-video-generation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        videoHistoryId: videoHistory.id,
        prompt: videoHistory.prompt 
      })
    });

    console.log('Background generation response status:', response.status);
    
    if (!response.ok) {
      const text = await response.text();
      console.error('Background generation failed:', text);
      return NextResponse.json(
        { error: 'Failed to trigger background generation', details: text },
        { status: 500 }
      );
    }

    const data = await response.json();
    console.log('Background generation response:', data);

    return NextResponse.json({
      success: true,
      message: 'Background generation triggered successfully',
      data: data
    });

  } catch (error) {
    console.error('Error in manual trigger:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
