import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { id: videoHistoryId } = await params;

    if (!videoHistoryId) {
      return NextResponse.json(
        { error: 'Video history ID is required' },
        { status: 400 }
      );
    }

    // Get video history record
    const { data: video, error } = await supabase
      .from('video_history')
      .select('*')
      .eq('id', videoHistoryId)
      .eq('user_id', user.id)
      .single();

    if (error || !video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      video: {
        id: video.id,
        status: video.status,
        prompt: video.prompt,
        original_video_url: video.original_video_url,
        original_download_url: video.original_download_url,
        stored_video_url: video.stored_video_url,
        error_message: video.error_message,
        created_at: video.created_at,
        updated_at: video.updated_at,
        processed_at: video.processed_at
      }
    });

  } catch (error) {
    console.error('Error fetching video status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
