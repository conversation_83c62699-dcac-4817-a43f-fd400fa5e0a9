import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export const maxDuration = 60; // 1 minute for initial request

export async function POST(request: Request) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { prompt } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    console.log('Starting async video generation for prompt:', prompt);

    // Create video history record immediately with 'pending' status
    const { data: videoHistory, error: historyError } = await supabase
      .from('video_history')
      .insert({
        user_id: user.id,
        prompt: prompt,
        original_video_url: '', // Will be updated when generation completes
        original_download_url: '', // Will be updated when generation completes
        status: 'pending'
      })
      .select()
      .single();

    if (historyError) {
      console.error('Failed to create video history:', historyError);
      return NextResponse.json(
        { error: 'Failed to create video record' },
        { status: 500 }
      );
    }

    // Trigger background video generation (fire and forget)
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    console.log('Triggering background generation for:', videoHistory.id);
    console.log('Base URL:', baseUrl);

    // First test if background API is accessible
    fetch(`${baseUrl}/api/test-background`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ test: 'connection', videoHistoryId: videoHistory.id })
    }).then(response => {
      console.log('Test background API response:', response.status);
      return response.json();
    }).then(data => {
      console.log('Test background API data:', data);

      // Now trigger actual video generation
      return fetch(`${baseUrl}/api/process-video-generation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoHistoryId: videoHistory.id,
          prompt: prompt
        })
      });
    }).then(response => {
      console.log('Background generation response status:', response.status);
      if (!response.ok) {
        return response.text().then(text => {
          console.error('Background generation failed:', text);
        });
      }
      return response.json().then(data => {
        console.log('Background generation started successfully:', data);
      });
    }).catch(error => {
      console.error('Failed to trigger background generation:', error);
    });

    // Return immediately with the video history ID
    return NextResponse.json({
      success: true,
      videoHistoryId: videoHistory.id,
      status: 'pending',
      message: '视频生成已开始，请稍后查看结果'
    });

  } catch (error) {
    console.error('Error in async video generation:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Failed to start video generation'
      },
      { status: 500 }
    );
  }
}
