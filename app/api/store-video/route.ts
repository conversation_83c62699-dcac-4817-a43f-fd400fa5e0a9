import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { storeVideoFromUrl } from '@/utils/videoStorageServer';

export const maxDuration = 300; // 5 minutes function timeout

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let videoHistoryId: string | undefined;

  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('[STORE-VIDEO] Unauthorized access attempt');
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const body = await request.json();
    videoHistoryId = body.videoHistoryId;

    if (!videoHistoryId) {
      console.error('[STORE-VIDEO] Missing video history ID');
      return NextResponse.json(
        { error: 'Video history ID is required' },
        { status: 400 }
      );
    }

    console.log(`[STORE-VIDEO] Starting storage process for video ${videoHistoryId} by user ${user.id}`);

    // Get video history record
    const { data: videoHistory, error: fetchError } = await supabase
      .from('video_history')
      .select('*')
      .eq('id', videoHistoryId)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !videoHistory) {
      console.error(`[STORE-VIDEO] Video history not found for ID ${videoHistoryId}:`, fetchError);
      return NextResponse.json(
        { error: 'Video history record not found' },
        { status: 404 }
      );
    }

    console.log(`[STORE-VIDEO] Found video history: status=${videoHistory.status}, original_url=${videoHistory.original_download_url}`);

    // Check if already processed
    if (videoHistory.status === 'completed') {
      console.log(`[STORE-VIDEO] Video ${videoHistoryId} already stored, returning existing URL`);
      return NextResponse.json({
        success: true,
        message: 'Video already stored',
        storedVideoUrl: videoHistory.stored_video_url
      });
    }

    // Validate that we have a download URL
    if (!videoHistory.original_download_url) {
      console.error(`[STORE-VIDEO] No download URL found for video ${videoHistoryId}`);
      await supabase
        .from('video_history')
        .update({
          status: 'failed',
          error_message: 'No download URL available',
          updated_at: new Date().toISOString()
        })
        .eq('id', videoHistoryId);

      return NextResponse.json(
        { error: 'No download URL available for this video' },
        { status: 400 }
      );
    }

    // Update status to processing
    console.log(`[STORE-VIDEO] Updating status to processing for video ${videoHistoryId}`);
    await supabase
      .from('video_history')
      .update({
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', videoHistoryId);

    // Store the video
    console.log(`[STORE-VIDEO] Starting download and upload for video ${videoHistoryId} from ${videoHistory.original_download_url}`);
    const storageResult = await storeVideoFromUrl(
      videoHistory.original_download_url,
      user.id,
      videoHistory.id
    );

    console.log(`[STORE-VIDEO] Storage result for video ${videoHistoryId}:`, {
      success: storageResult.success,
      error: storageResult.error,
      fileSize: storageResult.fileSize
    });

    if (storageResult.success) {
      // Update video history with storage info
      const { error: updateError } = await supabase
        .from('video_history')
        .update({
          status: 'completed',
          stored_video_url: storageResult.storedVideoUrl,
          stored_video_path: storageResult.storedVideoPath,
          file_size: storageResult.fileSize,
          processed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', videoHistoryId);

      if (updateError) {
        console.error(`[STORE-VIDEO] Failed to update video history for ${videoHistoryId}:`, updateError);
        return NextResponse.json(
          { error: 'Failed to update video history' },
          { status: 500 }
        );
      }

      const duration = Date.now() - startTime;
      console.log(`[STORE-VIDEO] Video ${videoHistoryId} stored successfully in ${duration}ms. File size: ${storageResult.fileSize} bytes`);

      return NextResponse.json({
        success: true,
        message: 'Video stored successfully',
        storedVideoUrl: storageResult.storedVideoUrl,
        fileSize: storageResult.fileSize,
        processingTime: duration
      });

    } else {
      // Update status to failed
      await supabase
        .from('video_history')
        .update({
          status: 'failed',
          error_message: storageResult.error,
          updated_at: new Date().toISOString()
        })
        .eq('id', videoHistoryId);

      console.error(`[STORE-VIDEO] Video storage failed for ${videoHistoryId}:`, storageResult.error);

      return NextResponse.json(
        { error: storageResult.error || 'Failed to store video' },
        { status: 500 }
      );
    }

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[STORE-VIDEO] Unexpected error for video ${videoHistoryId} after ${duration}ms:`, error);

    // Try to update status to failed if we have the video ID
    if (videoHistoryId) {
      try {
        const supabase = await createClient();
        await supabase
          .from('video_history')
          .update({
            status: 'failed',
            error_message: error instanceof Error ? error.message : 'Unexpected error during storage',
            updated_at: new Date().toISOString()
          })
          .eq('id', videoHistoryId);
      } catch (updateError) {
        console.error(`[STORE-VIDEO] Failed to update error status for ${videoHistoryId}:`, updateError);
      }
    }

    return NextResponse.json(
      { error: 'Internal server error during video storage' },
      { status: 500 }
    );
  }
}
