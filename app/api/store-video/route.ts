import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { storeVideoFromUrl } from '@/utils/videoStorageServer';

export const maxDuration = 300; // 5 minutes function timeout

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { videoHistoryId } = await request.json();

    if (!videoHistoryId) {
      return NextResponse.json(
        { error: 'Video history ID is required' },
        { status: 400 }
      );
    }

    console.log('Processing video storage for history ID:', videoHistoryId);

    // Get video history record
    const { data: videoHistory, error: fetchError } = await supabase
      .from('video_history')
      .select('*')
      .eq('id', videoHistoryId)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !videoHistory) {
      console.error('Video history not found:', fetchError);
      return NextResponse.json(
        { error: 'Video history record not found' },
        { status: 404 }
      );
    }

    // Check if already processed
    if (videoHistory.status === 'completed') {
      return NextResponse.json({
        success: true,
        message: 'Video already stored',
        storedVideoUrl: videoHistory.stored_video_url
      });
    }

    // Update status to processing
    await supabase
      .from('video_history')
      .update({ 
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', videoHistoryId);

    // Store the video
    const storageResult = await storeVideoFromUrl(
      videoHistory.original_download_url,
      user.id,
      videoHistory.id
    );

    if (storageResult.success) {
      // Update video history with storage info
      const { error: updateError } = await supabase
        .from('video_history')
        .update({
          status: 'completed',
          stored_video_url: storageResult.storedVideoUrl,
          stored_video_path: storageResult.storedVideoPath,
          file_size: storageResult.fileSize,
          processed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', videoHistoryId);

      if (updateError) {
        console.error('Failed to update video history:', updateError);
        return NextResponse.json(
          { error: 'Failed to update video history' },
          { status: 500 }
        );
      }

      console.log('Video stored successfully for history ID:', videoHistoryId);

      return NextResponse.json({
        success: true,
        message: 'Video stored successfully',
        storedVideoUrl: storageResult.storedVideoUrl,
        fileSize: storageResult.fileSize
      });

    } else {
      // Update status to failed
      await supabase
        .from('video_history')
        .update({
          status: 'failed',
          error_message: storageResult.error,
          updated_at: new Date().toISOString()
        })
        .eq('id', videoHistoryId);

      console.error('Video storage failed:', storageResult.error);

      return NextResponse.json(
        { error: storageResult.error || 'Failed to store video' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in video storage API:', error);
    
    return NextResponse.json(
      { error: 'Internal server error during video storage' },
      { status: 500 }
    );
  }
}
