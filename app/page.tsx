'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import VideoGeneratorSync from './components/VideoGeneratorSync';
import VideoGallery from './components/VideoGallery';
import QuotaInfo from './components/QuotaInfo';
import UserMenu from './components/UserMenu';
import StatusIndicator from './components/StatusIndicator';
import { createClient } from '@/utils/supabase/client';


export default function Home() {
  const [loading, setLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);


  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        router.push('/auth');
        return;
      }

      // Check user approval status
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('status')
        .eq('id', user.id)
        .single();

      if (profile?.status === 'pending') {
        await supabase.auth.signOut();
        router.push('/auth?message=pending_approval');
        return;
      } else if (profile?.status === 'rejected') {
        await supabase.auth.signOut();
        router.push('/auth?message=account_rejected');
        return;
      }
      
      setLoading(false);
    };

    checkAuth();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT' || !session) {
        router.push('/auth');
      }
    });

    return () => subscription.unsubscribe();
  }, [router, supabase]);



  const handleVideoGenerated = (result: { videoHistoryId: string; videoUrl: string; downloadUrl: string }) => {
    console.log('Video generated:', result);
    // Trigger refresh of video gallery
    setRefreshTrigger(prev => prev + 1);
  };

  if (loading) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-white text-lg">加载中...</div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <UserMenu />
      <div className="container mx-auto px-4 py-12">
        <header className="text-center mb-12">
          <h1 className="text-5xl font-bold text-white mb-4">
            Veo 3.0 视频生成器
          </h1>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto mb-6">
            使用 Google 最新的 Veo 3.0 模型，将您的文字描述转换为高质量视频
          </p>
          <div className="flex justify-center gap-4">
            <a
              href="/history"
              className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              查看历史记录
            </a>
          </div>
        </header>

        <VideoGeneratorSync onVideoGenerated={handleVideoGenerated} />
        
        <QuotaInfo />

        <div className="flex justify-center mb-8">
          <StatusIndicator />
        </div>

        <VideoGallery refreshTrigger={refreshTrigger} />
      </div>
    </main>
  );
}
