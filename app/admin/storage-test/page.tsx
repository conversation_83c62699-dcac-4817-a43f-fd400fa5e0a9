'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface TestResult {
  name: string;
  status: 'passed' | 'failed' | 'warning';
  details: Record<string, unknown>;
  message: string;
}

interface DiagnosticsResult {
  timestamp: string;
  tests: TestResult[];
  summary: {
    passed: number;
    failed: number;
    warnings: number;
  };
}

export default function StorageTestPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<DiagnosticsResult | null>(null);
  const [error, setError] = useState('');
  const router = useRouter();

  const runDiagnostics = async () => {
    setLoading(true);
    setError('');
    setResults(null);

    try {
      const response = await fetch('/api/admin/storage-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 401) {
          router.push('/auth');
          return;
        }
        throw new Error(data.error || 'Diagnostics failed');
      }

      setResults(data.diagnostics);
    } catch (err) {
      console.error('Diagnostics error:', err);
      setError(err instanceof Error ? err.message : 'Failed to run diagnostics');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <span className="text-green-400">✓</span>;
      case 'failed':
        return <span className="text-red-400">✗</span>;
      case 'warning':
        return <span className="text-yellow-400">⚠</span>;
      default:
        return <span className="text-gray-400">?</span>;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed':
        return 'border-green-500 bg-green-900/20';
      case 'failed':
        return 'border-red-500 bg-red-900/20';
      case 'warning':
        return 'border-yellow-500 bg-yellow-900/20';
      default:
        return 'border-gray-500 bg-gray-900/20';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              存储诊断工具
            </h1>
            <p className="text-gray-400">
              检查 Supabase 存储配置和视频存储功能
            </p>
          </div>
          <button
            onClick={() => router.push('/admin')}
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            返回管理面板
          </button>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-white">
              运行诊断测试
            </h2>
            <button
              onClick={runDiagnostics}
              disabled={loading}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                loading
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {loading ? '运行中...' : '开始诊断'}
            </button>
          </div>
          
          {loading && (
            <div className="flex items-center gap-2 text-blue-400">
              <svg className="animate-spin w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              正在运行诊断测试...
            </div>
          )}

          {error && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-center gap-2 text-red-400">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{error}</span>
              </div>
            </div>
          )}
        </div>

        {results && (
          <div className="space-y-6">
            {/* Summary */}
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">诊断摘要</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{results.summary.passed}</div>
                  <div className="text-sm text-gray-400">通过</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-400">{results.summary.failed}</div>
                  <div className="text-sm text-gray-400">失败</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400">{results.summary.warnings}</div>
                  <div className="text-sm text-gray-400">警告</div>
                </div>
              </div>
              <div className="mt-4 text-sm text-gray-400">
                测试时间: {new Date(results.timestamp).toLocaleString('zh-CN')}
              </div>
            </div>

            {/* Test Results */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">详细结果</h3>
              {results.tests.map((test, index) => (
                <div
                  key={index}
                  className={`border rounded-xl p-4 ${getStatusColor(test.status)}`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(test.status)}
                      <h4 className="font-medium text-white">{test.name}</h4>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      test.status === 'passed' ? 'bg-green-600 text-white' :
                      test.status === 'failed' ? 'bg-red-600 text-white' :
                      'bg-yellow-600 text-white'
                    }`}>
                      {test.status.toUpperCase()}
                    </span>
                  </div>
                  
                  <p className="text-gray-300 mb-3">{test.message}</p>
                  
                  {Object.keys(test.details).length > 0 && (
                    <details className="text-sm">
                      <summary className="cursor-pointer text-gray-400 hover:text-gray-300">
                        查看详细信息
                      </summary>
                      <pre className="mt-2 p-3 bg-gray-900/50 rounded text-gray-300 overflow-x-auto">
                        {JSON.stringify(test.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>

            {/* Recommendations */}
            {results.summary.failed > 0 && (
              <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-yellow-400 mb-4">
                  建议修复步骤
                </h3>
                <ul className="space-y-2 text-gray-300">
                  <li>• 检查 <code className="bg-gray-700 px-2 py-1 rounded">SUPABASE_STORAGE_SETUP.md</code> 文件中的设置指南</li>
                  <li>• 确保在 Supabase Dashboard 中创建了名为 &quot;videos&quot; 的存储桶</li>
                  <li>• 验证存储桶权限策略是否正确配置</li>
                  <li>• 检查环境变量是否正确设置</li>
                  <li>• 确认服务角色密钥具有存储权限</li>
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
