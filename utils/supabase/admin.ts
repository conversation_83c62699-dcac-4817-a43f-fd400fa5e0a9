import { createAdminClient } from './admin-client';

export async function checkIsAdmin(userId: string): Promise<boolean> {
  try {
    // 使用管理员客户端来避免 RLS 策略问题
    const adminSupabase = createAdminClient();

    const { data, error } = await adminSupabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking admin status:', error);
      return false;
    }

    return data?.role === 'admin' && data?.status === 'approved';
  } catch (error) {
    console.error('Error in checkIsAdmin:', error);
    return false;
  }
}

export async function checkUserApprovalStatus(userId: string): Promise<'pending' | 'approved' | 'rejected' | null> {
  try {
    const adminSupabase = createAdminClient();

    const { data, error } = await adminSupabase
      .from('user_profiles')
      .select('status')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking user approval status:', error);
      return null;
    }

    return data?.status || null;
  } catch (error) {
    console.error('Error in checkUserApprovalStatus:', error);
    return null;
  }
}

export async function getRegistrationMode(): Promise<'open' | 'approval_required'> {
  try {
    const adminSupabase = createAdminClient();

    const { data, error } = await adminSupabase
      .from('app_settings')
      .select('registration_mode')
      .single();

    if (error) {
      console.error('Error getting registration mode:', error);
      return 'approval_required';
    }

    return data?.registration_mode || 'approval_required';
  } catch (error) {
    console.error('Error in getRegistrationMode:', error);
    return 'approval_required';
  }
}

export async function ensureUserProfile(userId: string, email: string): Promise<void> {
  try {
    const adminSupabase = createAdminClient();

    // 检查用户资料是否存在
    const { data: existingProfile } = await adminSupabase
      .from('user_profiles')
      .select('id')
      .eq('id', userId)
      .single();

    if (!existingProfile) {
      // 创建用户资料
      const role = email === '<EMAIL>' ? 'admin' : 'user';
      const status = email === '<EMAIL>' ? 'approved' : 'pending';

      const { error } = await adminSupabase
        .from('user_profiles')
        .insert({
          id: userId,
          email: email,
          role: role,
          status: status
        });

      if (error) {
        console.error('Error creating user profile:', error);
      }
    }
  } catch (error) {
    console.error('Error in ensureUserProfile:', error);
  }
}