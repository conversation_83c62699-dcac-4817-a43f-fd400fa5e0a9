// Client-side utility functions for video storage
// Server-side functions have been moved to API routes

export interface VideoStorageResult {
  success: boolean;
  storedVideoUrl?: string;
  storedVideoPath?: string;
  fileSize?: number;
  error?: string;
}

export interface VideoMetadata {
  duration?: number;
  width?: number;
  height?: number;
  format?: string;
}

/**
 * Gets video metadata (placeholder for future implementation)
 */
export async function getVideoMetadata(videoBuffer: ArrayBuffer): Promise<VideoMetadata> {
  // This is a placeholder. In a real implementation, you might use a library
  // like ffprobe or similar to extract video metadata
  return {
    duration: undefined,
    width: undefined,
    height: undefined,
    format: 'mp4'
  };
}

/**
 * Validates if a URL is from an allowed domain
 */
export function isAllowedVideoUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const allowedDomains = ['filesystem.site']; // Add more domains as needed
    return allowedDomains.includes(urlObj.hostname);
  } catch {
    return false;
  }
}

/**
 * Generates a unique video ID
 */
export function generateVideoId(): string {
  return `video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Formats file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
