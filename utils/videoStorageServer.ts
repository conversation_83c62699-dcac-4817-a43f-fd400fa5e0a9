import { createClient } from '@/utils/supabase/server';

export interface VideoStorageResult {
  success: boolean;
  storedVideoUrl?: string;
  storedVideoPath?: string;
  fileSize?: number;
  error?: string;
}

export interface VideoMetadata {
  duration?: number;
  width?: number;
  height?: number;
  format?: string;
}

/**
 * Downloads a video from an external URL and uploads it to Supabase storage
 */
export async function storeVideoFromUrl(
  videoUrl: string,
  userId: string,
  videoId: string
): Promise<VideoStorageResult> {
  try {
    console.log('Starting video storage process for:', videoUrl);

    // Validate URL
    if (!videoUrl || !videoUrl.startsWith('http')) {
      return {
        success: false,
        error: 'Invalid video URL provided'
      };
    }

    // Download video from external URL
    const response = await fetch(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Veo3VideoDownloader/1.0)',
      },
    });

    if (!response.ok) {
      return {
        success: false,
        error: `Failed to download video: ${response.status} ${response.statusText}`
      };
    }

    // Get video data as buffer
    const videoBuffer = await response.arrayBuffer();
    const fileSize = videoBuffer.byteLength;

    console.log(`Downloaded video: ${fileSize} bytes`);

    // Create Supabase client
    const supabase = await createClient();

    // Generate file path: userId/videoId.mp4
    const fileName = `${videoId}.mp4`;
    const filePath = `${userId}/${fileName}`;

    // Upload to Supabase storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('videos')
      .upload(filePath, videoBuffer, {
        contentType: 'video/mp4',
        upsert: false, // Don't overwrite existing files
      });

    if (uploadError) {
      console.error('Upload error:', uploadError);
      return {
        success: false,
        error: `Failed to upload video: ${uploadError.message}`
      };
    }

    // Get public URL for the uploaded video
    const { data: urlData } = supabase.storage
      .from('videos')
      .getPublicUrl(filePath);

    console.log('Video uploaded successfully:', filePath);

    return {
      success: true,
      storedVideoUrl: urlData.publicUrl,
      storedVideoPath: filePath,
      fileSize: fileSize
    };

  } catch (error) {
    console.error('Error in storeVideoFromUrl:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Deletes a video from Supabase storage
 */
export async function deleteStoredVideo(filePath: string): Promise<boolean> {
  try {
    const supabase = await createClient();

    const { error } = await supabase.storage
      .from('videos')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting video:', error);
      return false;
    }

    console.log('Video deleted successfully:', filePath);
    return true;
  } catch (error) {
    console.error('Error in deleteStoredVideo:', error);
    return false;
  }
}

/**
 * Gets video metadata (placeholder for future implementation)
 */
export async function getVideoMetadata(videoBuffer: ArrayBuffer): Promise<VideoMetadata> {
  // This is a placeholder. In a real implementation, you might use a library
  // like ffprobe or similar to extract video metadata
  return {
    duration: undefined,
    width: undefined,
    height: undefined,
    format: 'mp4'
  };
}

/**
 * Validates if a URL is from an allowed domain
 */
export function isAllowedVideoUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const allowedDomains = ['filesystem.site']; // Add more domains as needed
    return allowedDomains.includes(urlObj.hostname);
  } catch {
    return false;
  }
}

/**
 * Generates a unique video ID
 */
export function generateVideoId(): string {
  return `video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
