import { createClient } from '@/utils/supabase/server';

export interface VideoStorageResult {
  success: boolean;
  storedVideoUrl?: string;
  storedVideoPath?: string;
  fileSize?: number;
  error?: string;
}

export interface VideoMetadata {
  duration?: number;
  width?: number;
  height?: number;
  format?: string;
}

/**
 * Downloads a video from an external URL and uploads it to Supabase storage
 */
export async function storeVideoFromUrl(
  videoUrl: string,
  userId: string,
  videoId: string
): Promise<VideoStorageResult> {
  const startTime = Date.now();

  try {
    console.log(`[VIDEO-STORAGE] Starting storage process for video ${videoId} from ${videoUrl}`);

    // Validate URL
    if (!videoUrl || !videoUrl.startsWith('http')) {
      console.error(`[VIDEO-STORAGE] Invalid URL provided: ${videoUrl}`);
      return {
        success: false,
        error: 'Invalid video URL provided'
      };
    }

    // Validate allowed domains
    if (!isAllowedVideoUrl(videoUrl)) {
      console.error(`[VIDEO-STORAGE] URL from disallowed domain: ${videoUrl}`);
      return {
        success: false,
        error: 'Video URL from disallowed domain'
      };
    }

    // Download video from external URL
    console.log(`[VIDEO-STORAGE] Starting download from ${videoUrl}`);
    const downloadStartTime = Date.now();

    const response = await fetch(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Veo3VideoDownloader/1.0)',
      },
      // Add timeout
      signal: AbortSignal.timeout(300000) // 5 minutes timeout
    });

    if (!response.ok) {
      console.error(`[VIDEO-STORAGE] Download failed: ${response.status} ${response.statusText}`);
      return {
        success: false,
        error: `Failed to download video: ${response.status} ${response.statusText}`
      };
    }

    // Check content type
    const contentType = response.headers.get('content-type');
    console.log(`[VIDEO-STORAGE] Content-Type: ${contentType}`);

    if (contentType && !contentType.startsWith('video/')) {
      console.error(`[VIDEO-STORAGE] Invalid content type: ${contentType}`);
      return {
        success: false,
        error: `Invalid content type: ${contentType}. Expected video file.`
      };
    }

    // Get video data as buffer
    const videoBuffer = await response.arrayBuffer();
    const fileSize = videoBuffer.byteLength;
    const downloadDuration = Date.now() - downloadStartTime;

    console.log(`[VIDEO-STORAGE] Downloaded video: ${fileSize} bytes in ${downloadDuration}ms`);

    // Check file size limits (100MB max)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (fileSize > maxSize) {
      console.error(`[VIDEO-STORAGE] File too large: ${fileSize} bytes (max: ${maxSize})`);
      return {
        success: false,
        error: `File too large: ${Math.round(fileSize / 1024 / 1024)}MB (max: 100MB)`
      };
    }

    // Create Supabase client
    const supabase = await createClient();

    // Generate file path: userId/videoId.mp4
    const fileName = `${videoId}.mp4`;
    const filePath = `${userId}/${fileName}`;

    // Upload to Supabase storage
    console.log(`[VIDEO-STORAGE] Starting upload to Supabase: ${filePath}`);
    const uploadStartTime = Date.now();

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('videos')
      .upload(filePath, videoBuffer, {
        contentType: contentType || 'video/mp4',
        upsert: false, // Don't overwrite existing files
      });

    if (uploadError) {
      console.error(`[VIDEO-STORAGE] Upload error for ${filePath}:`, uploadError);
      return {
        success: false,
        error: `Failed to upload video: ${uploadError.message}`
      };
    }

    const uploadDuration = Date.now() - uploadStartTime;
    console.log(`[VIDEO-STORAGE] Upload completed in ${uploadDuration}ms: ${uploadData.path}`);

    // Get public URL for the uploaded video
    const { data: urlData } = supabase.storage
      .from('videos')
      .getPublicUrl(filePath);

    const totalDuration = Date.now() - startTime;
    console.log(`[VIDEO-STORAGE] Video storage completed successfully in ${totalDuration}ms: ${urlData.publicUrl}`);

    return {
      success: true,
      storedVideoUrl: urlData.publicUrl,
      storedVideoPath: filePath,
      fileSize: fileSize
    };

  } catch (error) {
    const totalDuration = Date.now() - startTime;
    console.error(`[VIDEO-STORAGE] Error in storeVideoFromUrl after ${totalDuration}ms:`, error);

    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;

      // Provide more specific error messages
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        errorMessage = 'Download timeout - video file may be too large or server is slow';
      } else if (error.message.includes('fetch')) {
        errorMessage = 'Failed to download video from external server';
      } else if (error.message.includes('storage')) {
        errorMessage = 'Failed to upload video to storage';
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Deletes a video from Supabase storage
 */
export async function deleteStoredVideo(filePath: string): Promise<boolean> {
  try {
    const supabase = await createClient();

    const { error } = await supabase.storage
      .from('videos')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting video:', error);
      return false;
    }

    console.log('Video deleted successfully:', filePath);
    return true;
  } catch (error) {
    console.error('Error in deleteStoredVideo:', error);
    return false;
  }
}

/**
 * Gets video metadata (placeholder for future implementation)
 */
export async function getVideoMetadata(videoBuffer: ArrayBuffer): Promise<VideoMetadata> {
  // This is a placeholder. In a real implementation, you might use a library
  // like ffprobe or similar to extract video metadata
  return {
    duration: undefined,
    width: undefined,
    height: undefined,
    format: 'mp4'
  };
}

/**
 * Validates if a URL is from an allowed domain
 */
export function isAllowedVideoUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const allowedDomains = ['filesystem.site']; // Add more domains as needed
    return allowedDomains.includes(urlObj.hostname);
  } catch {
    return false;
  }
}

/**
 * Generates a unique video ID
 */
export function generateVideoId(): string {
  return `video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
