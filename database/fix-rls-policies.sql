-- 修复 RLS 策略的无限递归问题
-- 需要在 Supabase Dashboard 的 SQL Editor 中运行

-- 1. 删除所有现有的策略
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON public.user_profiles;

-- 2. 创建简化的策略，避免递归
-- 用户可以查看和更新自己的资料
CREATE POLICY "Users can manage their own profile" ON public.user_profiles
    FOR ALL USING (auth.uid() = id);

-- 3. 为管理员创建特殊策略（使用 JWT claims 而不是查询表）
-- 这需要在用户登录时设置 JWT claims，或者使用更简单的方法

-- 临时解决方案：允许所有认证用户查看所有用户资料（仅用于管理功能）
CREATE POLICY "Authenticated users can view all profiles" ON public.user_profiles
    FOR SELECT USING (auth.role() = 'authenticated');

-- 只有用户自己可以更新自己的资料
CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- 只有用户自己可以插入自己的资料
CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 4. 修复 video_history 表的策略
DROP POLICY IF EXISTS "Users can view their own video history" ON public.video_history;
DROP POLICY IF EXISTS "Users can insert their own video history" ON public.video_history;
DROP POLICY IF EXISTS "Users can update their own video history" ON public.video_history;
DROP POLICY IF EXISTS "Users can delete their own video history" ON public.video_history;

-- 创建简化的 video_history 策略
CREATE POLICY "Users can manage their own video history" ON public.video_history
    FOR ALL USING (auth.uid() = user_id);

-- 5. 修复 app_settings 表的策略
DROP POLICY IF EXISTS "Everyone can view app settings" ON public.app_settings;
DROP POLICY IF EXISTS "Admins can update app settings" ON public.app_settings;

-- 允许所有人查看应用设置
CREATE POLICY "Anyone can view app settings" ON public.app_settings
    FOR SELECT USING (true);

-- 允许认证用户更新应用设置（在应用层面控制管理员权限）
CREATE POLICY "Authenticated users can update app settings" ON public.app_settings
    FOR UPDATE USING (auth.role() = 'authenticated');
